import { Navigation } from "@/components/navigation"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"
import { getHeroImage, getProductImage } from "@/lib/images"
import { FeaturedSection } from "@/components/featured-section"

export default function HomePage() {
  const heroImage = getHeroImage()
  const productImage = getProductImage('main')

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      <section className="relative h-screen flex items-center justify-center">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('${heroImage.src}')`,
          }}
        >
          <div className="absolute inset-0 bg-black/40"></div>
        </div>
        <div className="relative z-10 text-center text-white">
          <h1 className="text-6xl font-light italic">Don't Just Hear. Experience!</h1>
        </div>
      </section>

      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <div className="space-y-6 text-2xl leading-relaxed">
            <p>
              At Floaid, we believe in focus.
              <br />
              One product at a time, chosen with care,
              <br />
              marketed with heart, sold until the very last piece finds its home.
            </p>
            <p>
              Today, it's not just a device.
              <br />
              It's clarity. It's comfort. It's a chance to hear the world again.
            </p>
            <p>
              The Behind-the-Ear Digital Hearing Aid.
              <br />
              Discreet. Life-changing. When it's gone, it's gone.
              <br />
              Floaid brings what matters, one product at a time.
            </p>
          </div>
        </div>
      </section>

      <section id="featured-product" className="bg-card">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 items-start">
            <div className="flex flex-col justify-between h-full py-12 px-4">
              <div>
                <div className="space-y-4 text-lg font-red-hat-display">
                  <h4 className="text-3xl font-bold">Designed for All-Day Wear</h4>
                  <p>
                    A behind-the-ear hearing aid fits comfortably behind your ear, using a discreet tube to deliver
                    amplified sound directly into your ear canal. It's sleek, lightweight, and nearly invisible —
                    perfect for all-day use without discomfort.
                  </p>
                  <p>
                    Whether you're in a quiet room or a busy street, enjoy clear, natural sound that helps you stay
                    connected to the world around you.
                  </p>
                </div>
              </div>
              <div className="mt-8">
                <Link href="/catalog">
                  <Button size="lg" className="px-8 font-red-hat-display">
                    Shop hearing aids
                  </Button>
                </Link>
              </div>
            </div>
            <div className="relative mx-auto">
              <Image
                src={productImage.src}
                alt={productImage.alt}
                width={productImage.width}
                height={productImage.height}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      <FeaturedSection />
    </div>
  )
}
